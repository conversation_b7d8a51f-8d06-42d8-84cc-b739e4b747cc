{
  "name": "柴管家开发环境",
  "dockerComposeFile": [
    "../docker-compose.yml",
    "../docker-compose.dev.yml",
    "docker-compose.devcontainer.yml"
  ],
  "service": "backend",
  "workspaceFolder": "/app",
  "shutdownAction": "stopCompose",
  
  // 开发容器配置
  "customizations": {
    "vscode": {
      "extensions": [
        // Python开发
        "ms-python.python",
        "ms-python.vscode-pylance",
        "ms-python.black-formatter",
        "ms-python.isort",
        "ms-python.flake8",
        "ms-toolsai.jupyter",
        
        // 前端开发
        "bradlc.vscode-tailwindcss",
        "esbenp.prettier-vscode",
        "dbaeumer.vscode-eslint",
        "ms-vscode.vscode-typescript-next",
        
        // Docker和容器
        "ms-azuretools.vscode-docker",
        "ms-vscode-remote.remote-containers",
        
        // 数据库
        "ms-mssql.mssql",
        "cweijan.vscode-postgresql-client2",
        
        // Git和版本控制
        "eamodio.gitlens",
        "github.vscode-pull-request-github",
        
        // 通用工具
        "ms-vscode.vscode-json",
        "redhat.vscode-yaml",
        "ms-vscode.makefile-tools",
        "streetsidesoftware.code-spell-checker",
        
        // API开发
        "humao.rest-client",
        "42crunch.vscode-openapi",
        
        // 测试
        "ms-python.pytest",
        "hbenl.vscode-test-explorer"
      ],
      
      "settings": {
        // Python配置
        "python.defaultInterpreterPath": "/usr/local/bin/python",
        "python.linting.enabled": true,
        "python.linting.pylintEnabled": false,
        "python.linting.flake8Enabled": true,
        "python.formatting.provider": "black",
        "python.sortImports.args": ["--profile", "black"],
        "python.testing.pytestEnabled": true,
        "python.testing.pytestArgs": ["tests"],
        
        // 格式化配置
        "editor.formatOnSave": true,
        "editor.codeActionsOnSave": {
          "source.organizeImports": true
        },
        
        // 文件关联
        "files.associations": {
          "*.env*": "dotenv",
          "docker-compose*.yml": "dockercompose",
          "Dockerfile*": "dockerfile"
        },
        
        // 终端配置
        "terminal.integrated.defaultProfile.linux": "bash",
        "terminal.integrated.cwd": "/app",
        
        // 调试配置
        "python.analysis.autoImportCompletions": true,
        "python.analysis.typeCheckingMode": "basic"
      }
    }
  },
  
  // 端口转发
  "forwardPorts": [
    8000,  // FastAPI后端
    5173,  // Vite前端
    5432,  // PostgreSQL
    6379,  // Redis
    5672,  // RabbitMQ
    15672, // RabbitMQ Management
    5555,  // Celery Flower
    5050,  // PgAdmin
    8081   // Redis Commander
  ],
  
  // 端口属性
  "portsAttributes": {
    "8000": {
      "label": "FastAPI Backend",
      "onAutoForward": "notify"
    },
    "5173": {
      "label": "Vite Frontend",
      "onAutoForward": "openBrowser"
    },
    "5050": {
      "label": "PgAdmin",
      "onAutoForward": "silent"
    },
    "5555": {
      "label": "Celery Flower",
      "onAutoForward": "silent"
    },
    "8081": {
      "label": "Redis Commander",
      "onAutoForward": "silent"
    },
    "15672": {
      "label": "RabbitMQ Management",
      "onAutoForward": "silent"
    }
  },
  
  // 容器启动后执行的命令
  "postCreateCommand": "pip install -e . && pre-commit install",
  
  // 挂载配置
  "mounts": [
    "source=${localWorkspaceFolder}/.vscode,target=/app/.vscode,type=bind,consistency=cached",
    "source=${localWorkspaceFolder}/backend,target=/app,type=bind,consistency=cached"
  ],
  
  // 环境变量
  "containerEnv": {
    "PYTHONPATH": "/app",
    "DEVELOPMENT": "true"
  },
  
  // 用户配置
  "remoteUser": "chaiguanjia",
  "updateRemoteUserUID": true
}
