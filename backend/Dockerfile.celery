# 柴管家 Celery 异步任务处理容器配置
# 基于后端相同的基础镜像

# ================================
# 基础镜像阶段
# ================================
FROM python:3.11-slim as base

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
  PYTHONDONTWRITEBYTECODE=1 \
  PIP_NO_CACHE_DIR=1 \
  PIP_DISABLE_PIP_VERSION_CHECK=1 \
  C_FORCE_ROOT=1

# 配置国内镜像源
RUN echo "deb https://mirrors.aliyun.com/debian/ bookworm main" > /etc/apt/sources.list && \
  echo "deb https://mirrors.aliyun.com/debian/ bookworm-updates main" >> /etc/apt/sources.list && \
  echo "deb https://mirrors.aliyun.com/debian-security/ bookworm-security main" >> /etc/apt/sources.list

# 安装系统依赖
RUN apt-get update && apt-get install -y \
  build-essential \
  libpq-dev \
  curl \
  && rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN groupadd -r chaiguanjia && useradd -r -g chaiguanjia chaiguanjia

# 设置工作目录
WORKDIR /app

# ================================
# 依赖安装阶段
# ================================
FROM base as dependencies

# 复制依赖文件
COPY requirements.txt ./

# 安装Python依赖
RUN pip install --upgrade pip && \
  pip install -r requirements.txt

# ================================
# Celery Worker 阶段
# ================================
FROM dependencies as celery-worker

# 复制应用代码
COPY . .

# 更改文件所有权
RUN chown -R chaiguanjia:chaiguanjia /app

# 切换到应用用户
USER chaiguanjia

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
  CMD celery -A app.core.celery inspect ping || exit 1

# 启动Celery Worker
CMD ["celery", "-A", "app.core.celery", "worker", "--loglevel=info", "--concurrency=4"]

# ================================
# Celery Beat 阶段
# ================================
FROM dependencies as celery-beat

# 复制应用代码
COPY . .

# 更改文件所有权
RUN chown -R chaiguanjia:chaiguanjia /app

# 切换到应用用户
USER chaiguanjia

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
  CMD celery -A app.core.celery inspect ping || exit 1

# 启动Celery Beat调度器
CMD ["celery", "-A", "app.core.celery", "beat", "--loglevel=info"]

# ================================
# Celery Flower 监控阶段
# ================================
FROM dependencies as celery-flower

# 安装Flower监控工具
RUN pip install flower

# 复制应用代码
COPY . .

# 更改文件所有权
RUN chown -R chaiguanjia:chaiguanjia /app

# 切换到应用用户
USER chaiguanjia

# 暴露端口
EXPOSE 5555

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:5555 || exit 1

# 启动Flower监控界面
CMD ["celery", "-A", "app.core.celery", "flower", "--host=0.0.0.0", "--port=5555"]
