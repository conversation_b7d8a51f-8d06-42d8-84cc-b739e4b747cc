-- 柴管家项目开发环境种子数据
-- 用于开发环境的测试数据初始化

-- 连接到应用数据库
\c chaiguanjia_dev;

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 插入测试用户数据（仅在开发环境）
DO $$
BEGIN
    -- 检查是否已有数据
    IF NOT EXISTS (SELECT 1 FROM users WHERE username = 'admin') THEN
        
        -- 插入管理员用户
        INSERT INTO users (
            id,
            username,
            email,
            full_name,
            password_hash,
            is_active,
            is_verified,
            is_superuser,
            created_at,
            updated_at,
            is_deleted
        ) VALUES (
            gen_random_uuid(),
            'admin',
            '<EMAIL>',
            '系统管理员',
            '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6QJw/2Oy6W', -- password: admin123
            true,
            true,
            true,
            NOW(),
            NOW(),
            false
        );
        
        -- 插入测试用户
        INSERT INTO users (
            id,
            username,
            email,
            full_name,
            password_hash,
            is_active,
            is_verified,
            is_superuser,
            created_at,
            updated_at,
            is_deleted
        ) VALUES (
            gen_random_uuid(),
            'testuser',
            '<EMAIL>',
            '测试用户',
            '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6QJw/2Oy6W', -- password: test123
            true,
            true,
            false,
            NOW(),
            NOW(),
            false
        );
        
        -- 插入客服用户
        INSERT INTO users (
            id,
            username,
            email,
            full_name,
            password_hash,
            is_active,
            is_verified,
            is_superuser,
            created_at,
            updated_at,
            is_deleted
        ) VALUES (
            gen_random_uuid(),
            'service01',
            '<EMAIL>',
            '客服小王',
            '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6QJw/2Oy6W', -- password: service123
            true,
            true,
            false,
            NOW(),
            NOW(),
            false
        );
        
        RAISE NOTICE '开发环境测试用户创建完成';
        RAISE NOTICE '管理员账号: admin / admin123';
        RAISE NOTICE '测试账号: testuser / test123';
        RAISE NOTICE '客服账号: service01 / service123';
        
    ELSE
        RAISE NOTICE '测试用户已存在，跳过创建';
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '创建测试用户时出错: %', SQLERRM;
END $$;
