# 柴管家后端开发依赖
# 包含生产环境依赖
-r requirements.txt

# 代码格式化工具
black==23.11.0
isort==5.12.0

# 代码质量检查
flake8==6.1.0
flake8-docstrings==1.7.0
flake8-import-order==0.18.2
flake8-bugbear==23.11.28
flake8-comprehensions==3.14.0
flake8-simplify==0.21.0

# 类型检查
mypy==1.7.1
types-requests==*********
types-redis==********
types-python-dateutil==*********

# 测试框架
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
httpx==0.25.2  # 用于测试API

# 安全检查
bandit==1.7.5
safety==2.3.5

# 文档生成
sphinx==7.2.6
sphinx-rtd-theme==1.3.0

# 开发工具
pre-commit==3.6.0
pip-tools==7.3.0

# 调试工具
ipdb==0.13.13
rich==13.7.0
