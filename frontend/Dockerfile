# 柴管家前端 React 容器化配置
# 多阶段构建：开发环境 + 生产环境

# ================================
# 基础镜像阶段
# ================================
FROM node:18-alpine as base

# 设置环境变量
ENV NODE_ENV=development
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

# 配置npm国内镜像源
RUN npm config set registry https://registry.npmmirror.com

# 安装pnpm（更快的包管理器）
RUN corepack enable

# 设置工作目录
WORKDIR /app

# 创建应用用户
RUN addgroup -g 1001 -S chaiguanjia && \
  adduser -S chaiguanjia -u 1001

# ================================
# 依赖安装阶段
# ================================
FROM base as dependencies

# 复制包管理文件
COPY package.json package-lock.json ./

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# ================================
# 开发环境阶段
# ================================
FROM base as development

# 复制包管理文件
COPY package.json package-lock.json ./

# 安装所有依赖（包括开发依赖）
RUN npm ci && npm cache clean --force

# 复制源代码
COPY . .

# 更改文件所有权
RUN chown -R chaiguanjia:chaiguanjia /app

# 切换到应用用户
USER chaiguanjia

# 暴露端口
EXPOSE 5173

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:5173 || exit 1

# 启动命令（开发模式 - 支持热重载）
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0"]

# ================================
# 构建阶段
# ================================
FROM dependencies as build

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# ================================
# 生产环境阶段
# ================================
FROM nginx:alpine as production

# 复制自定义nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 从构建阶段复制构建产物
COPY --from=build /app/dist /usr/share/nginx/html

# 创建nginx用户
RUN addgroup -g 1001 -S chaiguanjia && \
  adduser -S chaiguanjia -u 1001

# 设置文件权限
RUN chown -R chaiguanjia:chaiguanjia /usr/share/nginx/html && \
  chown -R chaiguanjia:chaiguanjia /var/cache/nginx && \
  chown -R chaiguanjia:chaiguanjia /var/log/nginx && \
  chown -R chaiguanjia:chaiguanjia /etc/nginx/conf.d

# 切换到应用用户
USER chaiguanjia

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:80 || exit 1

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
