# 柴管家 (ChaiGuanJia)

> 🤖 智能化多渠道客服管理平台

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python](https://img.shields.io/badge/Python-3.11+-blue.svg)](https://www.python.org/downloads/)
[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-red.svg)](https://fastapi.tiangolo.com/)
[![React](https://img.shields.io/badge/React-18+-blue.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-4.9+-blue.svg)](https://www.typescriptlang.org/)

## 📖 项目简介

柴管家是一个现代化的智能客服管理平台，旨在帮助企业高效管理多渠道客户服务。通过AI技术和自动化工具，为客服团队提供统一的工作台，提升客户服务质量和效率。

### 🌟 核心特性

- **🔗 多渠道接入**：支持微信、QQ、钉钉、飞书等主流IM平台
- **🤖 AI智能辅助**：智能回复建议、情感分析、自动分类
- **📊 实时监控**：客服工作量统计、响应时间分析、客户满意度追踪
- **🛡️ 安全可靠**：企业级安全保障、数据加密、权限管理
- **🎯 高度可定制**：灵活的工作流配置、自定义规则引擎
- **📱 响应式设计**：支持桌面端和移动端，随时随地处理客户咨询

### 🏗️ 技术架构

- **前端**：React 18 + TypeScript + Vite + Zustand
- **后端**：Python 3.11 + FastAPI + SQLAlchemy + Alembic
- **数据库**：PostgreSQL 15+ + Redis 7+
- **消息队列**：RabbitMQ 3.12+
- **容器化**：Docker + Docker Compose
- **代码质量**：ESLint + Prettier + Black + MyPy + Pre-commit

## 🚀 快速开始

### 📋 环境要求

确保您的开发环境满足以下要求：

- **Node.js**: 18.0+ ([下载地址](https://nodejs.org/))
- **Python**: 3.11+ ([下载地址](https://www.python.org/downloads/))
- **Docker**: 20.10+ ([下载地址](https://www.docker.com/get-started))
- **Git**: 2.30+ ([下载地址](https://git-scm.com/downloads))

### ⚡ 一键启动

```bash
# 1. 克隆项目
git clone https://github.com/your-org/chaiguanjia.git
cd chaiguanjia

# 2. 启动开发环境（Docker方式，推荐）
make dev

# 3. 访问应用
# 前端应用: http://localhost:3000
# 后端API: http://localhost:8000
# API文档: http://localhost:8000/docs
```

### 🛠️ 本地开发设置

如果您希望在本地环境进行开发：

#### 前端设置

```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

#### 后端设置

```bash
# 进入后端目录
cd backend

# 创建虚拟环境
python -m venv .venv
source .venv/bin/activate  # Windows: .venv\Scripts\activate

# 安装依赖
pip install -r requirements-dev.txt

# 启动开发服务器
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

## 📚 文档导航

### 🔧 开发文档

- [开发环境搭建](docs/development/setup.md) - 详细的开发环境配置指南
- [代码贡献指南](CONTRIBUTING.md) - 如何为项目做贡献
- [代码规范](docs/COMMIT_CONVENTION.md) - 提交信息和代码风格规范
- [故障排除](docs/troubleshooting.md) - 常见问题和解决方案

### 🏛️ 架构文档

- [系统架构](docs/architecture/system-architecture.md) - 整体系统架构设计
- [API设计](docs/api/README.md) - RESTful API设计规范
- [数据库设计](docs/architecture/database-design.md) - 数据模型和关系设计

### 🚀 部署文档

- [Docker部署](docs/deployment/docker.md) - 使用Docker部署应用
- [生产环境部署](docs/deployment/production.md) - 生产环境部署指南
- [监控和日志](docs/deployment/monitoring.md) - 应用监控和日志管理

## 🎯 项目状态

### 📈 开发进度

- [x] 项目初始化和工具链配置
- [x] 基础架构设计
- [ ] 用户认证系统
- [ ] 渠道接入模块
- [ ] 消息处理引擎
- [ ] AI智能辅助功能
- [ ] 监控和统计模块

### 🔄 版本历史

查看 [CHANGELOG.md](CHANGELOG.md) 了解详细的版本更新记录。

## 🤝 参与贡献

我们欢迎所有形式的贡献！请阅读 [贡献指南](CONTRIBUTING.md) 了解如何参与项目开发。

### 🐛 问题反馈

如果您发现了bug或有功能建议，请通过以下方式反馈：

- [GitHub Issues](https://github.com/your-org/chaiguanjia/issues) - 提交bug报告或功能请求
- [GitHub Discussions](https://github.com/your-org/chaiguanjia/discussions) - 参与讨论和提问

### 👥 开发团队

- **项目负责人**: [@your-username](https://github.com/your-username)
- **技术架构师**: [@architect](https://github.com/architect)
- **前端开发**: [@frontend-dev](https://github.com/frontend-dev)
- **后端开发**: [@backend-dev](https://github.com/backend-dev)

## 📄 许可证

本项目采用 [MIT 许可证](LICENSE)。

## 🙏 致谢

感谢所有为项目做出贡献的开发者和社区成员！

---

<div align="center">
  <p>如果这个项目对您有帮助，请给我们一个 ⭐️</p>
  <p>Made with ❤️ by 柴管家团队</p>
</div>
