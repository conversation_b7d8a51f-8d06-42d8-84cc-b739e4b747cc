<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    
    <!-- Python 检查配置 -->
    <inspection_tool class="PyPep8Inspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <!-- 与 Black 冲突的规则 -->
          <option value="E203" />
          <option value="E501" />
          <option value="W503" />
          <option value="E231" />
        </list>
      </option>
    </inspection_tool>
    
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <!-- 允许的命名模式 -->
          <option value="N806" />
        </list>
      </option>
    </inspection_tool>
    
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <!-- 忽略的标识符 -->
        </list>
      </option>
    </inspection_tool>
    
    <inspection_tool class="PyTypeCheckerInspection" enabled="true" level="WARNING" enabled_by_default="true" />
    
    <inspection_tool class="PyDocstringTypesInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    
    <!-- JavaScript/TypeScript 检查配置 -->
    <inspection_tool class="JSHint" enabled="false" level="ERROR" enabled_by_default="false" />
    
    <inspection_tool class="ESLintInspection" enabled="true" level="WARNING" enabled_by_default="true" />
    
    <inspection_tool class="TsLint" enabled="false" level="WARNING" enabled_by_default="false" />
    
    <inspection_tool class="JSUnusedLocalSymbols" enabled="true" level="WARNING" enabled_by_default="true" />
    
    <inspection_tool class="JSUnusedGlobalSymbols" enabled="true" level="WARNING" enabled_by_default="true" />
    
    <!-- 通用检查配置 -->
    <inspection_tool class="SpellCheckingInspection" enabled="true" level="TYPO" enabled_by_default="true">
      <option name="processCode" value="true" />
      <option name="processLiterals" value="true" />
      <option name="processComments" value="true" />
    </inspection_tool>
    
    <inspection_tool class="TodoComment" enabled="true" level="WARNING" enabled_by_default="true" />
    
    <inspection_tool class="DuplicatedCode" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <Languages>
        <language minSize="50" name="Python" />
        <language minSize="50" name="JavaScript" />
        <language minSize="50" name="TypeScript" />
      </Languages>
    </inspection_tool>
    
    <!-- 文档检查 -->
    <inspection_tool class="MarkdownUnresolvedFileReference" enabled="true" level="WARNING" enabled_by_default="true" />
    
    <!-- 安全检查 -->
    <inspection_tool class="SecurityAdvisorInspection" enabled="true" level="WARNING" enabled_by_default="true" />
    
    <!-- 性能检查 -->
    <inspection_tool class="PyUnboundLocalVariableInspection" enabled="true" level="WARNING" enabled_by_default="true" />
    
    <inspection_tool class="PyBroadExceptionInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
  </profile>
</component>
