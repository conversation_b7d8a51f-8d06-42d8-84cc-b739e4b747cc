<component name="ProjectCodeStyleConfiguration">
  <code_scheme name="Project" version="173">
    <!-- Python 代码风格配置 -->
    <Python>
      <option name="OPTIMIZE_IMPORTS_SORT_NAMES_IN_FROM_IMPORTS" value="true" />
      <option name="OPTIMIZE_IMPORTS_JOIN_FROM_IMPORTS_WITH_SAME_SOURCE" value="true" />
      <option name="OPTIMIZE_IMPORTS_ALWAYS_SPLIT_FROM_IMPORTS" value="true" />
      <option name="USE_CONTINUATION_INDENT_FOR_ARGUMENTS" value="true" />
      <option name="USE_CONTINUATION_INDENT_FOR_PARAMETERS" value="true" />
      <option name="ALIGN_COLLECTIONS_AND_COMPREHENSIONS" value="true" />
      <option name="SPACE_AFTER_PY_COLON" value="true" />
      <option name="SPACE_BEFORE_PY_COLON" value="false" />
      <option name="SPACE_AROUND_EQ_IN_NAMED_PARAMETER" value="false" />
      <option name="SPACE_AROUND_EQ_IN_KEYWORD_ARGUMENT" value="false" />
      <option name="DICT_ALIGNMENT" value="0" />
      <option name="DICT_WRAPPING" value="1" />
      <option name="DICT_NEW_LINE_AFTER_LEFT_BRACE" value="true" />
      <option name="DICT_NEW_LINE_BEFORE_RIGHT_BRACE" value="true" />
    </Python>
    
    <!-- JavaScript/TypeScript 代码风格配置 -->
    <JSCodeStyleSettings version="0">
      <option name="USE_SEMICOLON_AFTER_STATEMENT" value="true" />
      <option name="FORCE_SEMICOLON_STYLE" value="true" />
      <option name="SPACE_BEFORE_FUNCTION_LEFT_PARENTH" value="false" />
      <option name="USE_DOUBLE_QUOTES" value="false" />
      <option name="FORCE_QUOTE_STYlE" value="true" />
      <option name="ENFORCE_TRAILING_COMMA" value="WhenMultiline" />
      <option name="SPACES_WITHIN_OBJECT_LITERAL_BRACES" value="true" />
      <option name="SPACES_WITHIN_IMPORTS" value="true" />
      <option name="IMPORT_SORT_MODULE_NAME" value="true" />
      <option name="IMPORT_SORT_MEMBERS" value="true" />
      <option name="IMPORT_USE_NODE_RESOLUTION" value="true" />
    </JSCodeStyleSettings>
    
    <!-- TypeScript 特定配置 -->
    <TypeScriptCodeStyleSettings version="0">
      <option name="USE_SEMICOLON_AFTER_STATEMENT" value="true" />
      <option name="FORCE_SEMICOLON_STYLE" value="true" />
      <option name="SPACE_BEFORE_FUNCTION_LEFT_PARENTH" value="false" />
      <option name="USE_DOUBLE_QUOTES" value="false" />
      <option name="FORCE_QUOTE_STYlE" value="true" />
      <option name="ENFORCE_TRAILING_COMMA" value="WhenMultiline" />
      <option name="SPACES_WITHIN_OBJECT_LITERAL_BRACES" value="true" />
      <option name="SPACES_WITHIN_IMPORTS" value="true" />
      <option name="IMPORT_SORT_MODULE_NAME" value="true" />
      <option name="IMPORT_SORT_MEMBERS" value="true" />
      <option name="USE_EXPLICIT_JS_EXTENSION" value="NEVER" />
    </TypeScriptCodeStyleSettings>
    
    <!-- 通用代码风格配置 -->
    <codeStyleSettings language="Python">
      <option name="RIGHT_MARGIN" value="88" />
      <option name="ALIGN_MULTILINE_PARAMETERS" value="false" />
      <option name="ALIGN_MULTILINE_PARAMETERS_IN_CALLS" value="false" />
      <option name="CALL_PARAMETERS_WRAP" value="1" />
      <option name="CALL_PARAMETERS_LPAREN_ON_NEXT_LINE" value="false" />
      <option name="CALL_PARAMETERS_RPAREN_ON_NEXT_LINE" value="false" />
      <option name="METHOD_PARAMETERS_WRAP" value="1" />
      <option name="METHOD_PARAMETERS_LPAREN_ON_NEXT_LINE" value="false" />
      <option name="METHOD_PARAMETERS_RPAREN_ON_NEXT_LINE" value="false" />
      <option name="METHOD_CALL_CHAIN_WRAP" value="1" />
      <option name="BINARY_OPERATION_WRAP" value="1" />
      <option name="BINARY_OPERATION_SIGN_ON_NEXT_LINE" value="false" />
      <option name="TERNARY_OPERATION_WRAP" value="1" />
      <option name="TERNARY_OPERATION_SIGNS_ON_NEXT_LINE" value="false" />
      <option name="KEEP_SIMPLE_BLOCKS_IN_ONE_LINE" value="false" />
      <option name="KEEP_SIMPLE_METHODS_IN_ONE_LINE" value="false" />
      <option name="KEEP_SIMPLE_CLASSES_IN_ONE_LINE" value="false" />
      <indentOptions>
        <option name="INDENT_SIZE" value="4" />
        <option name="CONTINUATION_INDENT_SIZE" value="4" />
        <option name="TAB_SIZE" value="4" />
        <option name="USE_TAB_CHARACTER" value="false" />
        <option name="SMART_TABS" value="false" />
        <option name="LABEL_INDENT_SIZE" value="0" />
        <option name="LABEL_INDENT_ABSOLUTE" value="false" />
        <option name="USE_RELATIVE_INDENTS" value="false" />
      </indentOptions>
    </codeStyleSettings>
    
    <codeStyleSettings language="JavaScript">
      <option name="RIGHT_MARGIN" value="80" />
      <option name="KEEP_BLANK_LINES_IN_CODE" value="1" />
      <option name="ALIGN_MULTILINE_PARAMETERS" value="false" />
      <option name="ALIGN_MULTILINE_PARAMETERS_IN_CALLS" value="false" />
      <option name="CALL_PARAMETERS_WRAP" value="1" />
      <option name="METHOD_PARAMETERS_WRAP" value="1" />
      <option name="METHOD_CALL_CHAIN_WRAP" value="1" />
      <option name="BINARY_OPERATION_WRAP" value="1" />
      <option name="TERNARY_OPERATION_WRAP" value="1" />
      <option name="ARRAY_INITIALIZER_WRAP" value="1" />
      <option name="ASSIGNMENT_WRAP" value="1" />
      <indentOptions>
        <option name="INDENT_SIZE" value="2" />
        <option name="CONTINUATION_INDENT_SIZE" value="2" />
        <option name="TAB_SIZE" value="2" />
        <option name="USE_TAB_CHARACTER" value="false" />
      </indentOptions>
    </codeStyleSettings>
    
    <codeStyleSettings language="TypeScript">
      <option name="RIGHT_MARGIN" value="80" />
      <option name="KEEP_BLANK_LINES_IN_CODE" value="1" />
      <option name="ALIGN_MULTILINE_PARAMETERS" value="false" />
      <option name="ALIGN_MULTILINE_PARAMETERS_IN_CALLS" value="false" />
      <option name="CALL_PARAMETERS_WRAP" value="1" />
      <option name="METHOD_PARAMETERS_WRAP" value="1" />
      <option name="METHOD_CALL_CHAIN_WRAP" value="1" />
      <option name="BINARY_OPERATION_WRAP" value="1" />
      <option name="TERNARY_OPERATION_WRAP" value="1" />
      <option name="ARRAY_INITIALIZER_WRAP" value="1" />
      <option name="ASSIGNMENT_WRAP" value="1" />
      <indentOptions>
        <option name="INDENT_SIZE" value="2" />
        <option name="CONTINUATION_INDENT_SIZE" value="2" />
        <option name="TAB_SIZE" value="2" />
        <option name="USE_TAB_CHARACTER" value="false" />
      </indentOptions>
    </codeStyleSettings>
  </code_scheme>
</component>
