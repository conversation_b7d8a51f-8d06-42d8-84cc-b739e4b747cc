# 柴管家项目 Docker Compose 配置
# 完整的开发环境容器编排

version: '3.8'

# ================================
# 网络配置
# ================================
networks:
  chaiguanjia-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ================================
# 数据卷配置
# ================================
volumes:
  postgres-data:
    driver: local
  redis-data:
    driver: local
  rabbitmq-data:
    driver: local
  nginx-logs:
    driver: local

# ================================
# 服务配置
# ================================
services:
  # ================================
  # 数据库服务
  # ================================
  postgres:
    image: postgres:15-alpine
    container_name: chaiguanjia-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-chaiguanjia}
      POSTGRES_USER: ${POSTGRES_USER:-chaiguanjia}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-chaiguanjia123}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./infrastructure/docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    networks:
      chaiguanjia-network:
        ipv4_address: ***********
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-chaiguanjia} -d ${POSTGRES_DB:-chaiguanjia}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # ================================
  # Redis 缓存服务
  # ================================
  redis:
    image: redis:7-alpine
    container_name: chaiguanjia-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis123}
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD:-redis123}
    volumes:
      - redis-data:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      chaiguanjia-network:
        ipv4_address: ***********
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # ================================
  # RabbitMQ 消息队列服务
  # ================================
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: chaiguanjia-rabbitmq
    restart: unless-stopped
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER:-chaiguanjia}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD:-rabbitmq123}
      RABBITMQ_DEFAULT_VHOST: ${RABBITMQ_VHOST:-chaiguanjia}
    volumes:
      - rabbitmq-data:/var/lib/rabbitmq
    ports:
      - "${RABBITMQ_PORT:-5672}:5672"
      - "${RABBITMQ_MANAGEMENT_PORT:-15672}:15672"
    networks:
      chaiguanjia-network:
        ipv4_address: ***********
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # ================================
  # 后端 FastAPI 服务
  # ================================
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: chaiguanjia-backend
    restart: unless-stopped
    environment:
      # 数据库配置
      DATABASE_URL: postgresql://${POSTGRES_USER:-chaiguanjia}:${POSTGRES_PASSWORD:-chaiguanjia123}@postgres:5432/${POSTGRES_DB:-chaiguanjia}
      # Redis配置
      REDIS_URL: redis://:${REDIS_PASSWORD:-redis123}@redis:6379/0
      # RabbitMQ配置
      CELERY_BROKER_URL: amqp://${RABBITMQ_USER:-chaiguanjia}:${RABBITMQ_PASSWORD:-rabbitmq123}@rabbitmq:5672/${RABBITMQ_VHOST:-chaiguanjia}
      CELERY_RESULT_BACKEND: redis://:${REDIS_PASSWORD:-redis123}@redis:6379/1
      # 应用配置
      APP_ENV: ${APP_ENV:-development}
      SECRET_KEY: ${SECRET_KEY:-your-secret-key-change-in-production}
      DEBUG: ${DEBUG:-true}
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
    volumes:
      - ./backend:/app:cached
      - /app/__pycache__
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    networks:
      chaiguanjia-network:
        ipv4_address: ***********
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ================================
  # 前端 React 服务
  # ================================
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development
    container_name: chaiguanjia-frontend
    restart: unless-stopped
    environment:
      VITE_API_BASE_URL: http://localhost:${BACKEND_PORT:-8000}
      VITE_APP_ENV: ${APP_ENV:-development}
    volumes:
      - ./frontend:/app:cached
      - /app/node_modules
    ports:
      - "${FRONTEND_PORT:-5173}:5173"
    networks:
      chaiguanjia-network:
        ipv4_address: ***********
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5173"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ================================
  # Celery Worker 异步任务处理
  # ================================
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile.celery
      target: celery-worker
    container_name: chaiguanjia-celery-worker
    restart: unless-stopped
    environment:
      # 数据库配置
      DATABASE_URL: postgresql://${POSTGRES_USER:-chaiguanjia}:${POSTGRES_PASSWORD:-chaiguanjia123}@postgres:5432/${POSTGRES_DB:-chaiguanjia}
      # Redis配置
      REDIS_URL: redis://:${REDIS_PASSWORD:-redis123}@redis:6379/0
      # RabbitMQ配置
      CELERY_BROKER_URL: amqp://${RABBITMQ_USER:-chaiguanjia}:${RABBITMQ_PASSWORD:-rabbitmq123}@rabbitmq:5672/${RABBITMQ_VHOST:-chaiguanjia}
      CELERY_RESULT_BACKEND: redis://:${REDIS_PASSWORD:-redis123}@redis:6379/1
      # 应用配置
      APP_ENV: ${APP_ENV:-development}
      SECRET_KEY: ${SECRET_KEY:-your-secret-key-change-in-production}
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
    volumes:
      - ./backend:/app:cached
    networks:
      chaiguanjia-network:
        ipv4_address: ***********
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy

  # ================================
  # Celery Beat 定时任务调度
  # ================================
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile.celery
      target: celery-beat
    container_name: chaiguanjia-celery-beat
    restart: unless-stopped
    environment:
      # 数据库配置
      DATABASE_URL: postgresql://${POSTGRES_USER:-chaiguanjia}:${POSTGRES_PASSWORD:-chaiguanjia123}@postgres:5432/${POSTGRES_DB:-chaiguanjia}
      # Redis配置
      REDIS_URL: redis://:${REDIS_PASSWORD:-redis123}@redis:6379/0
      # RabbitMQ配置
      CELERY_BROKER_URL: amqp://${RABBITMQ_USER:-chaiguanjia}:${RABBITMQ_PASSWORD:-rabbitmq123}@rabbitmq:5672/${RABBITMQ_VHOST:-chaiguanjia}
      CELERY_RESULT_BACKEND: redis://:${REDIS_PASSWORD:-redis123}@redis:6379/1
      # 应用配置
      APP_ENV: ${APP_ENV:-development}
      SECRET_KEY: ${SECRET_KEY:-your-secret-key-change-in-production}
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
    volumes:
      - ./backend:/app:cached
    networks:
      chaiguanjia-network:
        ipv4_address: ***********
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy

  # ================================
  # Celery Flower 监控界面
  # ================================
  celery-flower:
    build:
      context: ./backend
      dockerfile: Dockerfile.celery
      target: celery-flower
    container_name: chaiguanjia-celery-flower
    restart: unless-stopped
    environment:
      # RabbitMQ配置
      CELERY_BROKER_URL: amqp://${RABBITMQ_USER:-chaiguanjia}:${RABBITMQ_PASSWORD:-rabbitmq123}@rabbitmq:5672/${RABBITMQ_VHOST:-chaiguanjia}
      CELERY_RESULT_BACKEND: redis://:${REDIS_PASSWORD:-redis123}@redis:6379/1
      # Flower配置
      FLOWER_BASIC_AUTH: ${FLOWER_USER:-admin}:${FLOWER_PASSWORD:-flower123}
    ports:
      - "${FLOWER_PORT:-5555}:5555"
    networks:
      chaiguanjia-network:
        ipv4_address: ***********
    depends_on:
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy

  # ================================
  # Nginx 反向代理服务
  # ================================
  nginx:
    image: nginx:alpine
    container_name: chaiguanjia-nginx
    restart: unless-stopped
    volumes:
      - ./infrastructure/docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./infrastructure/docker/nginx/conf.d:/etc/nginx/conf.d:ro
      - nginx-logs:/var/log/nginx
    ports:
      - "${NGINX_HTTP_PORT:-80}:80"
      - "${NGINX_HTTPS_PORT:-443}:443"
    networks:
      chaiguanjia-network:
        ipv4_address: ***********
    depends_on:
      - frontend
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
