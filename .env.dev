# 柴管家项目环境变量配置示例
# 复制此文件为 .env 并根据实际环境修改配置

# ================================
# 应用基础配置
# ================================
APP_ENV=development
DEBUG=true
LOG_LEVEL=INFO
SECRET_KEY=your-secret-key-change-in-production-must-be-32-chars

# ================================
# 数据库配置 (PostgreSQL)
# ================================
POSTGRES_DB=chaiguanjia
POSTGRES_USER=chaiguanjia
POSTGRES_PASSWORD=chaiguanjia123
POSTGRES_PORT=5432

# ================================
# Redis 缓存配置
# ================================
REDIS_PASSWORD=redis123
REDIS_PORT=6379

# ================================
# RabbitMQ 消息队列配置
# ================================
RABBITMQ_USER=chaiguanjia
RABBITMQ_PASSWORD=rabbitmq123
RABBITMQ_VHOST=chaiguanjia
RABBITMQ_PORT=5672
RABBITMQ_MANAGEMENT_PORT=15672

# ================================
# 应用服务端口配置
# ================================
BACKEND_PORT=8000
FRONTEND_PORT=5173
FLOWER_PORT=5555
NGINX_HTTP_PORT=80
NGINX_HTTPS_PORT=443

# ================================
# Celery Flower 监控配置
# ================================
FLOWER_USER=admin
FLOWER_PASSWORD=flower123

# ================================
# JWT 认证配置
# ================================
JWT_SECRET_KEY=your-jwt-secret-key-change-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# ================================
# 文件上传配置
# ================================
MAX_UPLOAD_SIZE=10485760
UPLOAD_PATH=/app/uploads

# ================================
# 邮件服务配置 (可选)
# ================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_TLS=true

# ================================
# 外部API配置 (可选)
# ================================
# AI服务配置
OPENAI_API_KEY=your-openai-api-key
OPENAI_BASE_URL=https://api.openai.com/v1

# 微信API配置
WECHAT_APP_ID=your-wechat-app-id
WECHAT_APP_SECRET=your-wechat-app-secret

# ================================
# 监控和日志配置
# ================================
SENTRY_DSN=your-sentry-dsn-url
PROMETHEUS_ENABLED=true
METRICS_PORT=9090

# ================================
# 开发环境特定配置
# ================================
# 是否启用API文档
ENABLE_DOCS=true
# 是否启用调试工具
ENABLE_DEBUG_TOOLBAR=true
# 是否启用热重载
ENABLE_RELOAD=true
