{
  "version": "2.0.0",
  "tasks": [
    // ===== 前端任务 =====
    {
      "label": "前端: 安装依赖",
      "type": "shell",
      "command": "npm",
      "args": ["install"],
      "options": {
        "cwd": "${workspaceFolder}/frontend"
      },
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "problemMatcher": []
    },
    {
      "label": "前端: 启动开发服务器",
      "type": "shell",
      "command": "npm",
      "args": ["run", "dev"],
      "options": {
        "cwd": "${workspaceFolder}/frontend"
      },
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "dedicated"
      },
      "isBackground": true,
      "problemMatcher": {
        "owner": "typescript",
        "source": "ts",
        "fileLocation": ["relative", "${workspaceFolder}/frontend"],
        "pattern": {
          "regexp": "\\[(.*)\\]\\s+(.*)",
          "file": 1,
          "message": 2
        },
        "background": {
          "activeOnStart": true,
          "beginsPattern": ".*Local:.*",
          "endsPattern": ".*ready in.*"
        }
      }
    },
    {
      "label": "前端: 代码检查",
      "type": "shell",
      "command": "npm",
      "args": ["run", "lint"],
      "options": {
        "cwd": "${workspaceFolder}/frontend"
      },
      "group": "test",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "problemMatcher": ["$eslint-stylish"]
    },
    {
      "label": "前端: 格式化代码",
      "type": "shell",
      "command": "npm",
      "args": ["run", "format"],
      "options": {
        "cwd": "${workspaceFolder}/frontend"
      },
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "silent",
        "focus": false,
        "panel": "shared"
      },
      "problemMatcher": []
    },
    {
      "label": "前端: 运行测试",
      "type": "shell",
      "command": "npm",
      "args": ["run", "test"],
      "options": {
        "cwd": "${workspaceFolder}/frontend"
      },
      "group": "test",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "problemMatcher": []
    },
    {
      "label": "前端: 构建项目",
      "type": "shell",
      "command": "npm",
      "args": ["run", "build"],
      "options": {
        "cwd": "${workspaceFolder}/frontend"
      },
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "problemMatcher": ["$tsc"]
    },

    // ===== 后端任务 =====
    {
      "label": "后端: 安装依赖",
      "type": "shell",
      "command": "pip",
      "args": ["install", "-r", "requirements-dev.txt"],
      "options": {
        "cwd": "${workspaceFolder}/backend"
      },
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "problemMatcher": []
    },
    {
      "label": "后端: 启动开发服务器",
      "type": "shell",
      "command": "uvicorn",
      "args": ["app.main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"],
      "options": {
        "cwd": "${workspaceFolder}/backend"
      },
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "dedicated"
      },
      "isBackground": true,
      "problemMatcher": {
        "pattern": {
          "regexp": ".*",
          "file": 1,
          "location": 2,
          "message": 3
        },
        "background": {
          "activeOnStart": true,
          "beginsPattern": ".*INFO.*Uvicorn running on.*",
          "endsPattern": ".*INFO.*Application startup complete.*"
        }
      }
    },
    {
      "label": "后端: 代码检查",
      "type": "shell",
      "command": "flake8",
      "args": ["."],
      "options": {
        "cwd": "${workspaceFolder}/backend"
      },
      "group": "test",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "problemMatcher": {
        "owner": "flake8",
        "fileLocation": ["relative", "${workspaceFolder}/backend"],
        "pattern": {
          "regexp": "^(.+):(\\d+):(\\d+):\\s+(\\w+\\d+)\\s+(.+)$",
          "file": 1,
          "line": 2,
          "column": 3,
          "code": 4,
          "message": 5
        }
      }
    },
    {
      "label": "后端: 格式化代码",
      "type": "shell",
      "command": "black",
      "args": ["."],
      "options": {
        "cwd": "${workspaceFolder}/backend"
      },
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "silent",
        "focus": false,
        "panel": "shared"
      },
      "problemMatcher": []
    },
    {
      "label": "后端: 运行测试",
      "type": "shell",
      "command": "pytest",
      "args": ["--cov=app", "--cov-report=term-missing"],
      "options": {
        "cwd": "${workspaceFolder}/backend"
      },
      "group": "test",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "problemMatcher": []
    },

    // ===== 通用任务 =====
    {
      "label": "Docker: 启动开发环境",
      "type": "shell",
      "command": "./scripts/docker-start.sh",
      "args": ["dev", "-d"],
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "problemMatcher": []
    },
    {
      "label": "Docker: 启动服务",
      "type": "shell",
      "command": "docker-compose",
      "args": ["up", "-d"],
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "problemMatcher": []
    },
    {
      "label": "Docker: 停止服务",
      "type": "shell",
      "command": "docker-compose",
      "args": ["down"],
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "problemMatcher": []
    },
    {
      "label": "Docker: 重置环境",
      "type": "shell",
      "command": "./scripts/docker-start.sh",
      "args": ["--reset"],
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "problemMatcher": []
    },
    {
      "label": "Docker: 健康检查",
      "type": "shell",
      "command": "./scripts/health-check.sh",
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "group": "test",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "problemMatcher": []
    },
    {
      "label": "Docker: 查看日志",
      "type": "shell",
      "command": "docker-compose",
      "args": ["logs", "-f"],
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "group": "test",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "dedicated"
      },
      "isBackground": true,
      "problemMatcher": []
    },
    {
      "label": "Pre-commit: 运行所有检查",
      "type": "shell",
      "command": "pre-commit",
      "args": ["run", "--all-files"],
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "group": "test",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "problemMatcher": []
    }
  ]
}
