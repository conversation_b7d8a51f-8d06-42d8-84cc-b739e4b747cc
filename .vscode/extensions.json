{
  "recommendations": [
    // ===== Python 开发 =====
    "ms-python.python",
    "ms-python.black-formatter",
    "ms-python.isort",
    "ms-python.flake8",
    "ms-python.mypy-type-checker",
    "ms-python.pylint",
    
    // ===== JavaScript/TypeScript 开发 =====
    "dbaeumer.vscode-eslint",
    "esbenp.prettier-vscode",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-typescript-next",
    
    // ===== React 开发 =====
    "dsznajder.es7-react-js-snippets",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    
    // ===== 代码质量 =====
    "streetsidesoftware.code-spell-checker",
    "editorconfig.editorconfig",
    "ms-vscode.vscode-json",
    
    // ===== Git 工具 =====
    "eamodio.gitlens",
    "mhutchie.git-graph",
    "donjayamanne.githistory",
    
    // ===== 文档和配置 =====
    "yzhang.markdown-all-in-one",
    "davidanson.vscode-markdownlint",
    "redhat.vscode-yaml",
    "tamasfe.even-better-toml",
    
    // ===== Docker 支持 =====
    "ms-azuretools.vscode-docker",
    "ms-vscode-remote.remote-containers",
    
    // ===== 数据库工具 =====
    "mtxr.sqltools",
    "mtxr.sqltools-driver-pg",
    
    // ===== 主题和图标 =====
    "pkief.material-icon-theme",
    "github.github-vscode-theme",
    
    // ===== 实用工具 =====
    "ms-vscode.vscode-todo-highlight",
    "gruntfuggly.todo-tree",
    "aaron-bond.better-comments",
    "usernamehw.errorlens",
    "christian-kohler.npm-intellisense",
    "ms-vscode.vscode-json",
    "ms-vscode.hexeditor",
    
    // ===== 测试工具 =====
    "ms-python.pytest",
    "orta.vscode-jest",
    
    // ===== API 开发 =====
    "humao.rest-client",
    "42crunch.vscode-openapi",
    
    // ===== 性能和调试 =====
    "ms-python.debugpy",
    "ms-vscode.js-debug",
    "ms-vscode.vscode-js-profile-flame"
  ],
  "unwantedRecommendations": [
    // 避免与项目配置冲突的扩展
    "ms-python.autopep8",
    "ms-python.yapf",
    "hookyqr.beautify",
    "vscode.typescript-language-features"
  ]
}
