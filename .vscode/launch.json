{"version": "0.2.0", "configurations": [{"name": "Python: FastAPI 应用", "type": "python", "request": "launch", "program": "${workspaceFolder}/backend/.venv/bin/uvicorn", "args": ["app.main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"], "cwd": "${workspaceFolder}/backend", "env": {"PYTHONPATH": "${workspaceFolder}/backend"}, "console": "integratedTerminal", "justMyCode": false, "django": false, "autoReload": {"enable": true}}, {"name": "Python: 当前文件", "type": "python", "request": "launch", "program": "${file}", "cwd": "${workspaceFolder}/backend", "env": {"PYTHONPATH": "${workspaceFolder}/backend"}, "console": "integratedTerminal", "justMyCode": true}, {"name": "Python: Pytest 当前文件", "type": "python", "request": "launch", "module": "pytest", "args": ["${file}", "-v", "-s"], "cwd": "${workspaceFolder}/backend", "env": {"PYTHONPATH": "${workspaceFolder}/backend"}, "console": "integratedTerminal", "justMyCode": false}, {"name": "Python: Pytest 所有测试", "type": "python", "request": "launch", "module": "pytest", "args": ["tests/", "-v", "--cov=app", "--cov-report=term-missing"], "cwd": "${workspaceFolder}/backend", "env": {"PYTHONPATH": "${workspaceFolder}/backend"}, "console": "integratedTerminal", "justMyCode": false}, {"name": "Node.js: 启动前端开发服务器", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/frontend", "runtimeExecutable": "npm", "runtimeArgs": ["run", "dev"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "port": 3000, "restart": true, "env": {"NODE_ENV": "development"}}, {"name": "Node.js: Jest 测试", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/frontend", "runtimeExecutable": "npm", "runtimeArgs": ["run", "test", "--", "--runInBand", "--no-cache", "--watchAll=false"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"name": "Chrome: 调试前端应用", "type": "chrome", "request": "launch", "url": "http://localhost:5173", "webRoot": "${workspaceFolder}/frontend/src", "sourceMapPathOverrides": {"webpack:///src/*": "${webRoot}/*"}, "preLaunchTask": "前端: 启动开发服务器"}, {"name": "Python: 容器内调试", "type": "python", "request": "attach", "connect": {"host": "localhost", "port": 5678}, "pathMappings": [{"localRoot": "${workspaceFolder}/backend", "remoteRoot": "/app"}], "justMyCode": false}, {"name": "Python: Celery 容器调试", "type": "python", "request": "attach", "connect": {"host": "localhost", "port": 5679}, "pathMappings": [{"localRoot": "${workspaceFolder}/backend", "remoteRoot": "/app"}], "justMyCode": false}, {"name": "Node.js: 容器内调试", "type": "node", "request": "attach", "port": 9229, "restart": true, "localRoot": "${workspaceFolder}/frontend", "remoteRoot": "/app"}, {"name": "Python: 附加到进程", "type": "python", "request": "attach", "connect": {"host": "localhost", "port": 5678}, "pathMappings": [{"localRoot": "${workspaceFolder}/backend", "remoteRoot": "/app"}], "justMyCode": false}, {"name": "Node.js: 附加到进程", "type": "node", "request": "attach", "port": 9229, "restart": true, "localRoot": "${workspaceFolder}/frontend", "remoteRoot": "/app"}], "compounds": [{"name": "启动全栈应用", "configurations": ["Python: FastAPI 应用", "Node.js: 启动前端开发服务器"], "stopAll": true}]}