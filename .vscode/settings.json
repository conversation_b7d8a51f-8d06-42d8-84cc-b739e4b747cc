{"editor.tabSize": 2, "editor.insertSpaces": true, "editor.detectIndentation": false, "editor.trimAutoWhitespace": true, "editor.formatOnSave": true, "editor.formatOnPaste": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": true, "source.organizeImports": true}, "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "files.trimFinalNewlines": true, "files.eol": "\n", "[python]": {"editor.tabSize": 4, "editor.defaultFormatter": "ms-python.black-formatter", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": true}}, "python.defaultInterpreterPath": "./backend/.venv/bin/python", "python.linting.enabled": true, "python.linting.flake8Enabled": true, "python.linting.mypyEnabled": true, "python.linting.banditEnabled": true, "python.linting.flake8Args": ["--config=backend/.flake8"], "python.linting.mypyArgs": ["--config-file=backend/pyproject.toml"], "python.formatting.provider": "none", "python.analysis.typeCheckingMode": "strict", "python.analysis.autoImportCompletions": true, "isort.args": ["--profile", "black"], "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "eslint.workingDirectories": ["frontend"], "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.tabSize": 2}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.tabSize": 2}, "[yaml]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.tabSize": 2}, "[markdown]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.wordWrap": "on", "editor.quickSuggestions": {"comments": "off", "strings": "off", "other": "off"}}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[scss]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "files.associations": {"*.env*": "dotenv", ".flake8": "ini", ".coveragerc": "ini", "Dockerfile*": "dockerfile", "docker-compose*.yml": "dockercompose", "docker-compose*.yaml": "dockercompose"}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true, "**/.venv": true, "**/venv": true, "**/__pycache__": true, "**/.pytest_cache": true, "**/.mypy_cache": true, "**/coverage": true, "**/.coverage": true, "**/htmlcov": true}, "files.exclude": {"**/__pycache__": true, "**/.pytest_cache": true, "**/.mypy_cache": true}, "git.autofetch": true, "git.enableSmartCommit": true, "git.confirmSync": false, "terminal.integrated.defaultProfile.osx": "zsh", "terminal.integrated.defaultProfile.linux": "bash", "terminal.integrated.defaultProfile.windows": "PowerShell", "terminal.integrated.cwd": "${workspaceFolder}", "terminal.integrated.env.osx": {"PYTHONPATH": "${workspaceFolder}/backend"}, "terminal.integrated.env.linux": {"PYTHONPATH": "${workspaceFolder}/backend"}, "docker.defaultRegistryPath": "", "docker.showStartPage": false, "emmet.includeLanguages": {"javascript": "javascriptreact", "typescript": "typescriptreact"}, "emmet.triggerExpansionOnTab": true, "explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"*.ts": "${capture}.js", "*.js": "${capture}.js.map, ${capture}.min.js, ${capture}.d.ts", "*.jsx": "${capture}.js", "*.tsx": "${capture}.ts", "tsconfig.json": "tsconfig.*.json", "package.json": "package-lock.json, yarn.lock, pnpm-lock.yaml", ".eslintrc.js": ".eslint<PERSON>ore, .eslintrc.*, eslint.config.*", ".prettierrc.js": ".prettieri<PERSON>re, .prettier<PERSON>.*", "pyproject.toml": "requirements*.txt, setup.py, setup.cfg, MANIFEST.in"}, "typescript.disableAutomaticTypeAcquisition": false, "typescript.preferences.includePackageJsonAutoImports": "auto", "python.analysis.autoSearchPaths": true, "python.analysis.diagnosticMode": "workspace"}