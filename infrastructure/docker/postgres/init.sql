-- 柴管家项目 PostgreSQL 初始化脚本
-- 创建数据库和基础配置

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- 创建应用数据库（如果不存在）
SELECT 'CREATE DATABASE chaiguanjia'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'chaiguanjia')\gexec

-- 连接到应用数据库
\c chaiguanjia;

-- 创建应用schema
CREATE SCHEMA IF NOT EXISTS app;
CREATE SCHEMA IF NOT EXISTS logs;

-- 设置默认搜索路径
ALTER DATABASE chaiguanjia SET search_path TO app, public;

-- 创建日志表
CREATE TABLE IF NOT EXISTS logs.application_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    level VARCHAR(20) NOT NULL,
    logger VARCHAR(100) NOT NULL,
    message TEXT NOT NULL,
    module VARCHAR(100),
    function_name VARCHAR(100),
    line_number INTEGER,
    user_id UUID,
    request_id UUID,
    extra_data JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 创建日志索引
CREATE INDEX IF NOT EXISTS idx_application_logs_timestamp ON logs.application_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_application_logs_level ON logs.application_logs(level);
CREATE INDEX IF NOT EXISTS idx_application_logs_user_id ON logs.application_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_application_logs_request_id ON logs.application_logs(request_id);

-- 创建性能监控表
CREATE TABLE IF NOT EXISTS logs.performance_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    endpoint VARCHAR(200) NOT NULL,
    method VARCHAR(10) NOT NULL,
    response_time_ms INTEGER NOT NULL,
    status_code INTEGER NOT NULL,
    user_id UUID,
    request_id UUID,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 创建性能监控索引
CREATE INDEX IF NOT EXISTS idx_performance_metrics_timestamp ON logs.performance_metrics(timestamp);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_endpoint ON logs.performance_metrics(endpoint);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_response_time ON logs.performance_metrics(response_time_ms);

-- 创建数据库函数：清理旧日志
CREATE OR REPLACE FUNCTION logs.cleanup_old_logs()
RETURNS void AS $$
BEGIN
    -- 删除30天前的应用日志
    DELETE FROM logs.application_logs 
    WHERE created_at < NOW() - INTERVAL '30 days';
    
    -- 删除7天前的性能指标
    DELETE FROM logs.performance_metrics 
    WHERE created_at < NOW() - INTERVAL '7 days';
END;
$$ LANGUAGE plpgsql;

-- 创建定时清理任务（需要pg_cron扩展，可选）
-- SELECT cron.schedule('cleanup-logs', '0 2 * * *', 'SELECT logs.cleanup_old_logs();');

-- 设置数据库参数优化
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
ALTER SYSTEM SET log_statement = 'mod';
ALTER SYSTEM SET log_min_duration_statement = 1000;
ALTER SYSTEM SET log_checkpoints = on;
ALTER SYSTEM SET log_connections = on;
ALTER SYSTEM SET log_disconnections = on;

-- 创建监控用户（只读权限）
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_roles WHERE rolname = 'monitor') THEN
        CREATE ROLE monitor WITH LOGIN PASSWORD 'monitor123';
        GRANT CONNECT ON DATABASE chaiguanjia TO monitor;
        GRANT USAGE ON SCHEMA logs TO monitor;
        GRANT SELECT ON ALL TABLES IN SCHEMA logs TO monitor;
        ALTER DEFAULT PRIVILEGES IN SCHEMA logs GRANT SELECT ON TABLES TO monitor;
    END IF;
END
$$;
